import React, { useState, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Play,
  Download,
  Heart,
  Upload,
  Wand2,
  Setting<PERSON>,
  Clock,
  Monitor,
  Square,
  Smartphone,
  Loader2,
  CheckCircle,
  AlertCircle,
  Film,
  Image as ImageIcon
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import {
  generateTextToVideo,
  generateImageToVideo,
  validateVideoFile,
  getResolutionOptions,
  getAspectRatioOptions,
  getConceptOptions,
  getDurationOptions,
  type LumaVideoResponse,
  type LumaTextToVideoOptions,
  type LumaImageToVideoOptions
} from "@/services/luma-service";

// Types for generated videos
interface GeneratedVideo {
  id: string;
  video_url: string;
  prompt: string;
  metadata?: any;
  timestamp: number;
  type: "text_to_video" | "image_to_video";
}

interface SavedVideo {
  id: string;
  video_url: string;
  prompt: string;
  metadata?: any;
  type: "text_to_video" | "image_to_video";
  timestamp: number;
}

function LumaLabsContent() {
  const { toast } = useToast();

  // State management
  const [activeTab, setActiveTab] = useState<"generate" | "saved">("generate");
  const [generationMode, setGenerationMode] = useState<"text" | "image">("text");
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentVideo, setCurrentVideo] = useState<GeneratedVideo | null>(null);
  const [savedVideos, setSavedVideos] = useState<SavedVideo[]>([]);

  // Text-to-video form state
  const [textPrompt, setTextPrompt] = useState("");
  const [textResolution, setTextResolution] = useState("720p");
  const [textDuration, setTextDuration] = useState(5);
  const [textAspectRatio, setTextAspectRatio] = useState("16:9");
  const [textLoop, setTextLoop] = useState(false);
  const [textConcepts, setTextConcepts] = useState<string[]>([]);

  // Image-to-video form state
  const [imagePrompt, setImagePrompt] = useState("");
  const [imageResolution, setImageResolution] = useState("720p");
  const [imageDuration, setImageDuration] = useState(5);
  const [imageAspectRatio, setImageAspectRatio] = useState("16:9");
  const [imageLoop, setImageLoop] = useState(false);
  const [keyframeType, setKeyframeType] = useState<"frame0" | "frame1" | "both">("frame0");
  const [frame0File, setFrame0File] = useState<File | null>(null);
  const [frame1File, setFrame1File] = useState<File | null>(null);

  // File input refs
  const frame0InputRef = useRef<HTMLInputElement>(null);
  const frame1InputRef = useRef<HTMLInputElement>(null);

  // Load saved videos from localStorage on component mount
  React.useEffect(() => {
    const saved = localStorage.getItem("luma-saved-videos");
    if (saved) {
      try {
        setSavedVideos(JSON.parse(saved));
      } catch (error) {
        console.error("Error loading saved videos:", error);
      }
    }
  }, []);

  // Save videos to localStorage
  const saveVideosToStorage = (videos: SavedVideo[]) => {
    localStorage.setItem("luma-saved-videos", JSON.stringify(videos));
    setSavedVideos(videos);
  };

  // Handle text-to-video generation
  const handleTextToVideoGeneration = async () => {
    if (!textPrompt.trim()) {
      toast({
        title: "Error",
        description: "Por favor ingresa una descripción para el video",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);

    try {
      const options: LumaTextToVideoOptions = {
        prompt: textPrompt,
        resolution: textResolution,
        duration: textDuration,
        aspect_ratio: textAspectRatio,
        loop: textLoop,
        concepts: textConcepts.length > 0 ? textConcepts : undefined,
      };

      const result = await generateTextToVideo(options);

      if (result.success && result.video_url) {
        const newVideo: GeneratedVideo = {
          id: Date.now().toString(),
          video_url: result.video_url,
          prompt: textPrompt,
          metadata: result.metadata,
          timestamp: Date.now(),
          type: "text_to_video",
        };

        setCurrentVideo(newVideo);

        toast({
          title: "¡Video generado!",
          description: "Tu video ha sido creado exitosamente",
        });
      } else {
        throw new Error(result.error || "Error desconocido");
      }
    } catch (error) {
      console.error("Error generating text-to-video:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Error al generar el video",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Handle image-to-video generation
  const handleImageToVideoGeneration = async () => {
    if (!imagePrompt.trim()) {
      toast({
        title: "Error",
        description: "Por favor ingresa una descripción para el video",
        variant: "destructive",
      });
      return;
    }

    // Validate required files
    if (keyframeType === "frame0" && !frame0File) {
      toast({
        title: "Error",
        description: "Por favor selecciona una imagen inicial",
        variant: "destructive",
      });
      return;
    }

    if (keyframeType === "frame1" && !frame1File) {
      toast({
        title: "Error",
        description: "Por favor selecciona una imagen final",
        variant: "destructive",
      });
      return;
    }

    if (keyframeType === "both" && (!frame0File || !frame1File)) {
      toast({
        title: "Error",
        description: "Por favor selecciona ambas imágenes (inicial y final)",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);

    try {
      const options: LumaImageToVideoOptions = {
        prompt: imagePrompt,
        resolution: imageResolution,
        duration: imageDuration,
        aspect_ratio: imageAspectRatio,
        loop: imageLoop,
        keyframe_type: keyframeType,
        frame0: frame0File || undefined,
        frame1: frame1File || undefined,
      };

      const result = await generateImageToVideo(options);

      if (result.success && result.video_url) {
        const newVideo: GeneratedVideo = {
          id: Date.now().toString(),
          video_url: result.video_url,
          prompt: imagePrompt,
          metadata: result.metadata,
          timestamp: Date.now(),
          type: "image_to_video",
        };

        setCurrentVideo(newVideo);

        toast({
          title: "¡Video generado!",
          description: "Tu video ha sido creado exitosamente",
        });
      } else {
        throw new Error(result.error || "Error desconocido");
      }
    } catch (error) {
      console.error("Error generating image-to-video:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Error al generar el video",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Handle file upload
  const handleFileUpload = (file: File, type: "frame0" | "frame1") => {
    const validation = validateVideoFile(file);
    if (!validation.valid) {
      toast({
        title: "Error",
        description: validation.error,
        variant: "destructive",
      });
      return;
    }

    if (type === "frame0") {
      setFrame0File(file);
    } else {
      setFrame1File(file);
    }

    toast({
      title: "Imagen cargada",
      description: `${file.name} ha sido cargada exitosamente`,
    });
  };

  // Handle video download
  const handleDownload = async () => {
    if (!currentVideo?.video_url) {
      toast({
        title: "❌ Error",
        description: "No hay video para descargar",
        variant: "destructive",
      });
      return;
    }

    try {
      // Use our backend proxy to download the video
      const proxyUrl = `/api/luma-labs/download-video?url=${encodeURIComponent(currentVideo.video_url)}`;

      const response = await fetch(proxyUrl, {
        method: 'GET',
        headers: {
          'Accept': 'video/*',
        },
      });

      if (!response.ok) {
        throw new Error(`Download error! status: ${response.status}`);
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      // Get filename from response headers or create default
      const contentDisposition = response.headers.get('content-disposition');
      let filename = `video-${Date.now()}.mp4`;
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }

      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast({
        title: "✅ Descarga completada",
        description: `Video descargado como ${filename}`,
      });
    } catch (error) {
      console.error('Download error:', error);
      toast({
        title: "❌ Error de descarga",
        description: "No se pudo descargar el video. Inténtalo de nuevo.",
        variant: "destructive",
      });
    }
  };

  // Handle save to favorites
  const handleSaveToFavorites = () => {
    if (!currentVideo) {
      toast({
        title: "Error",
        description: "No hay video para guardar",
        variant: "destructive",
      });
      return;
    }

    const savedVideo: SavedVideo = {
      id: currentVideo.id,
      video_url: currentVideo.video_url,
      prompt: currentVideo.prompt,
      metadata: currentVideo.metadata,
      type: currentVideo.type,
      timestamp: currentVideo.timestamp,
    };

    const updatedSaved = [savedVideo, ...savedVideos];
    saveVideosToStorage(updatedSaved);

    toast({
      title: "✅ Video guardado",
      description: "El video ha sido añadido a tus favoritos",
    });
  };

  // Handle remove from favorites
  const handleRemoveFromFavorites = (videoId: string) => {
    const updatedSaved = savedVideos.filter(video => video.id !== videoId);
    saveVideosToStorage(updatedSaved);

    toast({
      title: "Video eliminado",
      description: "El video ha sido eliminado de tus favoritos",
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          className="text-center mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex items-center justify-center mb-4">
            <div className="bg-gradient-to-r from-purple-600 to-blue-600 p-3 rounded-full mr-4">
              <Film className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
              Luma Labs Dream Machine
            </h1>
          </div>
          <p className="text-gray-600 text-lg max-w-2xl mx-auto">
            Genera videos cinematográficos de alta calidad a partir de texto o imágenes usando la tecnología más avanzada de Luma Labs
          </p>
        </motion.div>

        {/* Main Tabs */}
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as "generate" | "saved")} className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-8">
            <TabsTrigger value="generate" className="flex items-center gap-2">
              <Wand2 className="w-4 h-4" />
              Última Generación
            </TabsTrigger>
            <TabsTrigger value="saved" className="flex items-center gap-2">
              <Heart className="w-4 h-4" />
              Guardados ({savedVideos.length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="generate">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Control Panel */}
              <div className="lg:col-span-1">
                <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Settings className="w-5 h-5" />
                      Panel de Control
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Generation Mode Selector */}
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Modo de Generación</Label>
                      <Tabs value={generationMode} onValueChange={(value) => setGenerationMode(value as "text" | "image")}>
                        <TabsList className="grid w-full grid-cols-2">
                          <TabsTrigger value="text" className="flex items-center gap-2">
                            <Wand2 className="w-4 h-4" />
                            Texto a Video
                          </TabsTrigger>
                          <TabsTrigger value="image" className="flex items-center gap-2">
                            <ImageIcon className="w-4 h-4" />
                            Imagen a Video
                          </TabsTrigger>
                        </TabsList>
                      </Tabs>
                    </div>

                    {/* Text-to-Video Controls */}
                    {generationMode === "text" && (
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="text-prompt">Descripción del Video</Label>
                          <Textarea
                            id="text-prompt"
                            placeholder="Describe el video que quieres crear..."
                            value={textPrompt}
                            onChange={(e) => setTextPrompt(e.target.value)}
                            className="min-h-[100px]"
                          />
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label>Resolución</Label>
                            <Select value={textResolution} onValueChange={setTextResolution}>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {getResolutionOptions().map((option) => (
                                  <SelectItem key={option.value} value={option.value}>
                                    {option.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="space-y-2">
                            <Label>Duración</Label>
                            <Select value={textDuration.toString()} onValueChange={(value) => setTextDuration(parseInt(value))}>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {getDurationOptions().map((option) => (
                                  <SelectItem key={option.value} value={option.value.toString()}>
                                    {option.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label>Relación de Aspecto</Label>
                          <Select value={textAspectRatio} onValueChange={setTextAspectRatio}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {getAspectRatioOptions().map((option) => (
                                <SelectItem key={option.value} value={option.value}>
                                  <div className="flex items-center gap-2">
                                    {option.value === "16:9" && <Monitor className="w-4 h-4" />}
                                    {option.value === "1:1" && <Square className="w-4 h-4" />}
                                    {option.value === "9:16" && <Smartphone className="w-4 h-4" />}
                                    {option.label}
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="flex items-center justify-between">
                          <Label htmlFor="text-loop">Video en Bucle</Label>
                          <Switch
                            id="text-loop"
                            checked={textLoop}
                            onCheckedChange={setTextLoop}
                          />
                        </div>

                        <Button
                          onClick={handleTextToVideoGeneration}
                          disabled={isGenerating || !textPrompt.trim()}
                          className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                        >
                          {isGenerating ? (
                            <>
                              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                              Generando Video...
                            </>
                          ) : (
                            <>
                              <Play className="w-4 h-4 mr-2" />
                              Generar Video
                            </>
                          )}
                        </Button>
                      </div>
                    )}

                    {/* Image-to-Video Controls */}
                    {generationMode === "image" && (
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="image-prompt">Descripción del Video</Label>
                          <Textarea
                            id="image-prompt"
                            placeholder="Describe cómo debe moverse o transformarse la imagen..."
                            value={imagePrompt}
                            onChange={(e) => setImagePrompt(e.target.value)}
                            className="min-h-[100px]"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label>Tipo de Keyframes</Label>
                          <Select value={keyframeType} onValueChange={(value) => setKeyframeType(value as "frame0" | "frame1" | "both")}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="frame0">Solo Imagen Inicial</SelectItem>
                              <SelectItem value="frame1">Solo Imagen Final</SelectItem>
                              <SelectItem value="both">Imagen Inicial y Final</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        {/* File Upload Areas */}
                        {(keyframeType === "frame0" || keyframeType === "both") && (
                          <div className="space-y-2">
                            <Label>Imagen Inicial</Label>
                            <div
                              className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-purple-400 transition-colors"
                              onClick={() => frame0InputRef.current?.click()}
                            >
                              {frame0File ? (
                                <div className="space-y-2">
                                  <CheckCircle className="w-8 h-8 text-green-500 mx-auto" />
                                  <p className="text-sm font-medium">{frame0File.name}</p>
                                  <p className="text-xs text-gray-500">
                                    {(frame0File.size / 1024 / 1024).toFixed(2)} MB
                                  </p>
                                </div>
                              ) : (
                                <div className="space-y-2">
                                  <Upload className="w-8 h-8 text-gray-400 mx-auto" />
                                  <p className="text-sm text-gray-600">
                                    Haz clic para subir imagen inicial
                                  </p>
                                  <p className="text-xs text-gray-500">
                                    PNG, JPG, WebP hasta 50MB
                                  </p>
                                </div>
                              )}
                            </div>
                            <input
                              ref={frame0InputRef}
                              type="file"
                              accept="image/*"
                              className="hidden"
                              onChange={(e) => {
                                const file = e.target.files?.[0];
                                if (file) handleFileUpload(file, "frame0");
                              }}
                            />
                          </div>
                        )}

                        {(keyframeType === "frame1" || keyframeType === "both") && (
                          <div className="space-y-2">
                            <Label>Imagen Final</Label>
                            <div
                              className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-purple-400 transition-colors"
                              onClick={() => frame1InputRef.current?.click()}
                            >
                              {frame1File ? (
                                <div className="space-y-2">
                                  <CheckCircle className="w-8 h-8 text-green-500 mx-auto" />
                                  <p className="text-sm font-medium">{frame1File.name}</p>
                                  <p className="text-xs text-gray-500">
                                    {(frame1File.size / 1024 / 1024).toFixed(2)} MB
                                  </p>
                                </div>
                              ) : (
                                <div className="space-y-2">
                                  <Upload className="w-8 h-8 text-gray-400 mx-auto" />
                                  <p className="text-sm text-gray-600">
                                    Haz clic para subir imagen final
                                  </p>
                                  <p className="text-xs text-gray-500">
                                    PNG, JPG, WebP hasta 50MB
                                  </p>
                                </div>
                              )}
                            </div>
                            <input
                              ref={frame1InputRef}
                              type="file"
                              accept="image/*"
                              className="hidden"
                              onChange={(e) => {
                                const file = e.target.files?.[0];
                                if (file) handleFileUpload(file, "frame1");
                              }}
                            />
                          </div>
                        )}

                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label>Resolución</Label>
                            <Select value={imageResolution} onValueChange={setImageResolution}>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {getResolutionOptions().map((option) => (
                                  <SelectItem key={option.value} value={option.value}>
                                    {option.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="space-y-2">
                            <Label>Duración</Label>
                            <Select value={imageDuration.toString()} onValueChange={(value) => setImageDuration(parseInt(value))}>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {getDurationOptions().map((option) => (
                                  <SelectItem key={option.value} value={option.value.toString()}>
                                    {option.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label>Relación de Aspecto</Label>
                          <Select value={imageAspectRatio} onValueChange={setImageAspectRatio}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {getAspectRatioOptions().map((option) => (
                                <SelectItem key={option.value} value={option.value}>
                                  <div className="flex items-center gap-2">
                                    {option.value === "16:9" && <Monitor className="w-4 h-4" />}
                                    {option.value === "1:1" && <Square className="w-4 h-4" />}
                                    {option.value === "9:16" && <Smartphone className="w-4 h-4" />}
                                    {option.label}
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="flex items-center justify-between">
                          <Label htmlFor="image-loop">Video en Bucle</Label>
                          <Switch
                            id="image-loop"
                            checked={imageLoop}
                            onCheckedChange={setImageLoop}
                          />
                        </div>

                        <Button
                          onClick={handleImageToVideoGeneration}
                          disabled={isGenerating || !imagePrompt.trim()}
                          className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                        >
                          {isGenerating ? (
                            <>
                              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                              Generando Video...
                            </>
                          ) : (
                            <>
                              <Play className="w-4 h-4 mr-2" />
                              Generar Video
                            </>
                          )}
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Video Preview */}
              <div className="lg:col-span-2">
                <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Play className="w-5 h-5" />
                      Vista Previa del Video
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {currentVideo ? (
                      <div className="space-y-4">
                        {/* Video Player */}
                        <div className="relative bg-black rounded-lg overflow-hidden">
                          <video
                            src={currentVideo.video_url}
                            controls
                            className="w-full h-auto max-h-96"
                            poster="/placeholder-video.jpg"
                          >
                            Tu navegador no soporta el elemento de video.
                          </video>
                        </div>

                        {/* Video Info */}
                        <div className="space-y-3">
                          <div>
                            <h3 className="font-semibold text-gray-800 mb-1">Descripción:</h3>
                            <p className="text-gray-600 text-sm">{currentVideo.prompt}</p>
                          </div>

                          {currentVideo.metadata && (
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                              <div>
                                <span className="font-medium text-gray-700">Resolución:</span>
                                <p className="text-gray-600">{currentVideo.metadata.resolution}</p>
                              </div>
                              <div>
                                <span className="font-medium text-gray-700">Duración:</span>
                                <p className="text-gray-600">{currentVideo.metadata.duration}s</p>
                              </div>
                              <div>
                                <span className="font-medium text-gray-700">Aspecto:</span>
                                <p className="text-gray-600">{currentVideo.metadata.aspect_ratio}</p>
                              </div>
                              <div>
                                <span className="font-medium text-gray-700">Tipo:</span>
                                <p className="text-gray-600">
                                  {currentVideo.type === "text_to_video" ? "Texto a Video" : "Imagen a Video"}
                                </p>
                              </div>
                            </div>
                          )}

                          {/* Action Buttons */}
                          <div className="flex flex-wrap gap-3">
                            <Button
                              onClick={handleDownload}
                              className="bg-green-600 hover:bg-green-700 text-white"
                            >
                              <Download className="w-4 h-4 mr-2" />
                              Descargar
                            </Button>
                            <Button
                              onClick={handleSaveToFavorites}
                              variant="outline"
                              className="border-purple-200 text-purple-700 hover:bg-purple-50"
                            >
                              <Heart className="w-4 h-4 mr-2" />
                              Guardar
                            </Button>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-12">
                        <div className="bg-gray-100 rounded-full p-6 w-24 h-24 mx-auto mb-4 flex items-center justify-center">
                          <Film className="w-12 h-12 text-gray-400" />
                        </div>
                        <h3 className="text-lg font-semibold text-gray-700 mb-2">
                          No hay video generado
                        </h3>
                        <p className="text-gray-500 text-sm">
                          Configura los parámetros y genera tu primer video
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="saved">
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Heart className="w-5 h-5" />
                  Videos Guardados ({savedVideos.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                {savedVideos.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {savedVideos.map((video) => (
                      <motion.div
                        key={video.id}
                        className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.3 }}
                      >
                        <div className="relative">
                          <video
                            src={video.video_url}
                            className="w-full h-48 object-cover"
                            poster="/placeholder-video.jpg"
                          />
                          <div className="absolute top-2 right-2">
                            <Badge variant="secondary" className="text-xs">
                              {video.type === "text_to_video" ? "Texto" : "Imagen"}
                            </Badge>
                          </div>
                        </div>

                        <div className="p-4">
                          <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                            {video.prompt}
                          </p>

                          <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
                            <span>{new Date(video.timestamp).toLocaleDateString()}</span>
                            {video.metadata && (
                              <span>{video.metadata.resolution}</span>
                            )}
                          </div>

                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              onClick={() => setCurrentVideo({
                                id: video.id,
                                video_url: video.video_url,
                                prompt: video.prompt,
                                metadata: video.metadata,
                                timestamp: video.timestamp,
                                type: video.type,
                              })}
                              className="flex-1 bg-purple-600 hover:bg-purple-700 text-white"
                            >
                              <Play className="w-3 h-3 mr-1" />
                              Ver
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleRemoveFromFavorites(video.id)}
                              className="text-red-600 border-red-200 hover:bg-red-50"
                            >
                              <AlertCircle className="w-3 h-3" />
                            </Button>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="bg-gray-100 rounded-full p-6 w-24 h-24 mx-auto mb-4 flex items-center justify-center">
                      <Heart className="w-12 h-12 text-gray-400" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-700 mb-2">
                      No hay videos guardados
                    </h3>
                    <p className="text-gray-500 text-sm">
                      Los videos que guardes aparecerán aquí
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}

export default function LumaLabsPage() {
  return (
    <DashboardLayout pageTitle="Luma Labs Dream Machine">
      <LumaLabsContent />
    </DashboardLayout>
  );
}
