/**
 * Service for Luma Labs video generation API
 */

const API_BASE_URL = "/api/luma-labs";

export interface LumaTextToVideoOptions {
  prompt: string;
  resolution?: string;
  duration?: number;
  aspect_ratio?: string;
  loop?: boolean;
  concepts?: string[];
}

export interface LumaImageToVideoOptions {
  prompt: string;
  resolution?: string;
  duration?: number;
  aspect_ratio?: string;
  loop?: boolean;
  keyframe_type: "frame0" | "frame1" | "both";
  frame0?: File;
  frame1?: File;
}

export interface LumaVideoResponse {
  success: boolean;
  video_url?: string;
  generation_id?: string;
  status?: string;
  metadata?: {
    model: string;
    resolution: string;
    duration: number;
    aspect_ratio: string;
    loop: boolean;
    original_prompt: string;
    enhanced_prompt: string;
    generation_type: string;
    concepts?: string[];
    keyframes?: Record<string, string>;
  };
  error?: string;
}

export interface LumaGenerationStatus {
  success: boolean;
  generation_id: string;
  status: string;
  data?: any;
  error?: string;
}

/**
 * Generate a video from text using Luma Labs Dream Machine
 */
export async function generateTextToVideo(options: LumaTextToVideoOptions): Promise<LumaVideoResponse> {
  try {
    const formData = new FormData();
    formData.append("prompt", options.prompt);

    if (options.resolution) {
      formData.append("resolution", options.resolution);
    }
    if (options.duration) {
      formData.append("duration", options.duration.toString());
    }
    if (options.aspect_ratio) {
      formData.append("aspect_ratio", options.aspect_ratio);
    }
    if (options.loop !== undefined) {
      formData.append("loop", options.loop.toString());
    }
    if (options.concepts && options.concepts.length > 0) {
      formData.append("concepts", options.concepts.join(","));
    }

    const response = await fetch(`${API_BASE_URL}/generate-text-to-video`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`HTTP error! status: ${response.status}, body: ${errorText}`);

      // Try to parse error message from response
      try {
        const errorData = JSON.parse(errorText);
        throw new Error(errorData.detail || errorData.error || `HTTP error! status: ${response.status}`);
      } catch {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    }

    const result = await response.json();
    console.log("Text-to-video result:", result);
    return result;
  } catch (error) {
    console.error("Error generating text-to-video:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Generate a video from image keyframes using Luma Labs Dream Machine
 */
export async function generateImageToVideo(options: LumaImageToVideoOptions): Promise<LumaVideoResponse> {
  try {
    const formData = new FormData();
    formData.append("prompt", options.prompt);
    formData.append("keyframe_type", options.keyframe_type);

    if (options.resolution) {
      formData.append("resolution", options.resolution);
    }
    if (options.duration) {
      formData.append("duration", options.duration.toString());
    }
    if (options.aspect_ratio) {
      formData.append("aspect_ratio", options.aspect_ratio);
    }
    if (options.loop !== undefined) {
      formData.append("loop", options.loop.toString());
    }

    // Add image files based on keyframe type
    if (options.keyframe_type === "frame0" || options.keyframe_type === "both") {
      if (!options.frame0) {
        throw new Error("frame0 image is required for this keyframe type");
      }
      formData.append("frame0", options.frame0);
    }

    if (options.keyframe_type === "frame1" || options.keyframe_type === "both") {
      if (!options.frame1) {
        throw new Error("frame1 image is required for this keyframe type");
      }
      formData.append("frame1", options.frame1);
    }

    const response = await fetch(`${API_BASE_URL}/generate-image-to-video`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`HTTP error! status: ${response.status}, body: ${errorText}`);

      // Try to parse error message from response
      try {
        const errorData = JSON.parse(errorText);
        throw new Error(errorData.detail || errorData.error || `HTTP error! status: ${response.status}`);
      } catch {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    }

    const result = await response.json();
    console.log("Image-to-video result:", result);
    return result;
  } catch (error) {
    console.error("Error generating image-to-video:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Get the status of a video generation
 */
export async function getGenerationStatus(generationId: string): Promise<LumaGenerationStatus> {
  try {
    const response = await fetch(`${API_BASE_URL}/status/${generationId}`, {
      method: "GET",
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error("Error getting generation status:", error);
    return {
      success: false,
      generation_id: generationId,
      status: "error",
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Validate video file for upload
 */
export function validateVideoFile(file: File): { valid: boolean; error?: string } {
  const maxSize = 50 * 1024 * 1024; // 50MB
  const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/webp"];

  if (file.size > maxSize) {
    return {
      valid: false,
      error: "File size must be less than 50MB",
    };
  }

  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: "File must be JPEG, PNG, or WebP format",
    };
  }

  return { valid: true };
}

/**
 * Get available resolution options
 */
export function getResolutionOptions() {
  return [
    { value: "720p", label: "HD (720p)" },
    { value: "1080p", label: "Full HD (1080p)" },
    { value: "4K", label: "Ultra HD (4K)" },
  ];
}

/**
 * Get available aspect ratio options
 */
export function getAspectRatioOptions() {
  return [
    { value: "16:9", label: "Widescreen (16:9)" },
    { value: "1:1", label: "Square (1:1)" },
    { value: "9:16", label: "Vertical (9:16)" },
  ];
}

/**
 * Get available concept options
 */
export function getConceptOptions() {
  return [
    { value: "dolly_zoom", label: "Dolly Zoom" },
  ];
}

/**
 * Get available duration options (Luma Labs only supports 5s and 9s)
 */
export function getDurationOptions() {
  return [
    { value: 5, label: "5 segundos" },
    { value: 9, label: "9 segundos" },
  ];
}
