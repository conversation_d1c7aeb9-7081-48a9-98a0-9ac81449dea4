"""
Service for creating videos using Luma Labs Dream Machine API.
Supports text-to-video and image-to-video generation with various quality options.
"""

import logging
import httpx
import asyncio
import time
from typing import Optional, Dict, Any, List
from fastapi import UploadFile

logger = logging.getLogger(__name__)


class LumaService:
    """Service for creating videos using Luma Labs Dream Machine API."""

    def __init__(self):
        self.api_key = "luma-a1556580-ebd5-4cb0-8f73-28c8b0a5a053-e69dcd01-9184-4c8f-af5e-6897cdadf2bb"
        self.base_url = "https://api.lumalabs.ai/dream-machine/v1/generations"
        self.model = "ray-2"
        
    async def generate_text_to_video(
        self,
        prompt: str,
        resolution: str = "720p",
        duration: int = 5,
        aspect_ratio: str = "16:9",
        loop: bool = False,
        concepts: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Generate a video from text using Luma Labs Dream Machine.

        Args:
            prompt: Text description of the video to create
            resolution: Video resolution (720p, 1080p, 4K)
            duration: Video duration in seconds (5 or 9 only)
            aspect_ratio: Video aspect ratio (16:9, 1:1, 9:16)
            loop: Whether the video should loop seamlessly
            concepts: Special effects like "dolly_zoom"

        Returns:
            Dict with success status, video data, and metadata
        """
        if not self.api_key:
            logger.error("Luma Labs API key not configured")
            return {"success": False, "error": "Luma Labs API key not configured"}

        try:
            # Convert duration to string format required by Luma Labs API
            duration_str = f"{duration}s"

            # Validate duration (only 5s and 9s are supported)
            if duration not in [5, 9]:
                logger.error(f"Invalid duration: {duration}. Only 5 and 9 seconds are supported.")
                return {"success": False, "error": "Duration must be 5 or 9 seconds"}

            # Enhance prompt for video generation
            video_prompt = f"Cinematic video: {prompt}. High-quality, professional cinematography with smooth motion and engaging visual storytelling."

            # Prepare request payload according to Luma Labs API format
            payload = {
                "model": self.model,
                "prompt": video_prompt,
                "duration": duration_str,
                "loop": loop
            }

            # Add aspect_ratio if provided (Luma Labs uses different format)
            if aspect_ratio:
                payload["aspect_ratio"] = aspect_ratio

            # Add concepts if provided
            if concepts and len(concepts) > 0:
                payload["concepts"] = concepts

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            logger.info(f"🎬 Generating text-to-video with Luma Labs: {prompt[:100]}...")
            logger.info(f"Payload: {payload}")

            async with httpx.AsyncClient(timeout=300.0) as client:
                response = await client.post(
                    self.base_url,
                    json=payload,
                    headers=headers
                )

                logger.info(f"Response status: {response.status_code}")
                logger.info(f"Response text: {response.text}")

                if response.status_code not in [200, 201]:
                    error_text = response.text
                    logger.error(f"Luma Labs error {response.status_code}: {error_text}")
                    return {"success": False, "error": f"Luma Labs error: {error_text}"}

                result = response.json()
                generation_id = result.get("id")

                if not generation_id:
                    logger.error(f"No generation ID in response: {result}")
                    return {"success": False, "error": "No generation ID in response"}

                # Poll for completion
                video_result = await self._poll_for_completion(generation_id)

                if video_result["success"]:
                    video_result["metadata"] = {
                        "model": self.model,
                        "resolution": resolution,
                        "duration": duration,
                        "aspect_ratio": aspect_ratio,
                        "loop": loop,
                        "concepts": concepts,
                        "original_prompt": prompt,
                        "enhanced_prompt": video_prompt,
                        "generation_type": "text_to_video"
                    }

                return video_result

        except Exception as e:
            logger.error(f"Error generating text-to-video: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}

    async def generate_image_to_video(
        self,
        prompt: str,
        keyframes: Dict[str, str],
        resolution: str = "720p",
        duration: int = 5,
        aspect_ratio: str = "16:9",
        loop: bool = False
    ) -> Dict[str, Any]:
        """
        Generate a video from image keyframes using Luma Labs Dream Machine.

        Args:
            prompt: Text description of the video to create
            keyframes: Dict with frame positions and image URLs/base64
            resolution: Video resolution (720p, 1080p, 4K)
            duration: Video duration in seconds (5 or 9 only)
            aspect_ratio: Video aspect ratio (16:9, 1:1, 9:16)
            loop: Whether the video should loop seamlessly

        Returns:
            Dict with success status, video data, and metadata
        """
        if not self.api_key:
            logger.error("Luma Labs API key not configured")
            return {"success": False, "error": "Luma Labs API key not configured"}

        try:
            # Convert duration to string format required by Luma Labs API
            duration_str = f"{duration}s"

            # Validate duration (only 5s and 9s are supported)
            if duration not in [5, 9]:
                logger.error(f"Invalid duration: {duration}. Only 5 and 9 seconds are supported.")
                return {"success": False, "error": "Duration must be 5 or 9 seconds"}

            # Enhance prompt for video generation
            video_prompt = f"Cinematic video based on keyframes: {prompt}. Smooth transitions between frames with professional cinematography and engaging motion."

            # Prepare request payload according to Luma Labs API format
            payload = {
                "model": self.model,
                "prompt": video_prompt,
                "keyframes": keyframes,
                "duration": duration_str,
                "loop": loop
            }

            # Add aspect_ratio if provided
            if aspect_ratio:
                payload["aspect_ratio"] = aspect_ratio

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            logger.info(f"🎬 Generating image-to-video with Luma Labs: {prompt[:100]}...")
            logger.info(f"Payload: {payload}")

            async with httpx.AsyncClient(timeout=300.0) as client:
                response = await client.post(
                    self.base_url,
                    json=payload,
                    headers=headers
                )

                logger.info(f"Response status: {response.status_code}")
                logger.info(f"Response text: {response.text}")

                if response.status_code not in [200, 201]:
                    error_text = response.text
                    logger.error(f"Luma Labs error {response.status_code}: {error_text}")
                    return {"success": False, "error": f"Luma Labs error: {error_text}"}

                result = response.json()
                generation_id = result.get("id")

                if not generation_id:
                    logger.error(f"No generation ID in response: {result}")
                    return {"success": False, "error": "No generation ID in response"}

                # Poll for completion
                video_result = await self._poll_for_completion(generation_id)

                if video_result["success"]:
                    video_result["metadata"] = {
                        "model": self.model,
                        "resolution": resolution,
                        "duration": duration,
                        "aspect_ratio": aspect_ratio,
                        "loop": loop,
                        "keyframes": keyframes,
                        "original_prompt": prompt,
                        "enhanced_prompt": video_prompt,
                        "generation_type": "image_to_video"
                    }

                return video_result

        except Exception as e:
            logger.error(f"Error generating image-to-video: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}

    async def _poll_for_completion(self, generation_id: str, max_wait_time: int = 300) -> Dict[str, Any]:
        """
        Poll the Luma Labs API for generation completion.

        Args:
            generation_id: The ID of the generation to poll
            max_wait_time: Maximum time to wait in seconds

        Returns:
            Dict with success status and video data
        """
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        start_time = time.time()
        poll_interval = 5  # Start with 5 second intervals

        while time.time() - start_time < max_wait_time:
            try:
                async with httpx.AsyncClient(timeout=30.0) as client:
                    response = await client.get(
                        f"{self.base_url}/{generation_id}",
                        headers=headers
                    )

                    if response.status_code != 200:
                        logger.error(f"Error polling generation {generation_id}: {response.status_code}")
                        return {"success": False, "error": f"Polling error: {response.status_code}"}

                    result = response.json()
                    status = result.get("state", "").lower()

                    logger.info(f"Generation {generation_id} status: {status}")
                    logger.info(f"Full result: {result}")

                    if status == "completed":
                        # Check for video URL in different possible locations
                        video_url = None
                        if "assets" in result and result["assets"]:
                            video_url = result["assets"].get("video")
                        elif "video" in result:
                            video_url = result["video"]
                        elif "output" in result:
                            video_url = result["output"]

                        if video_url:
                            return {
                                "success": True,
                                "video_url": video_url,
                                "generation_id": generation_id,
                                "status": status,
                                "result_data": result
                            }
                        else:
                            logger.error(f"No video URL found in completed result: {result}")
                            return {"success": False, "error": "No video URL in completed result"}

                    elif status == "failed":
                        error_msg = result.get("failure_reason", result.get("error", "Generation failed"))
                        logger.error(f"Generation {generation_id} failed: {error_msg}")
                        return {"success": False, "error": f"Generation failed: {error_msg}"}

                    elif status in ["queued", "dreaming", "pending", "processing"]:
                        # Still processing, wait and continue
                        await asyncio.sleep(poll_interval)
                        # Gradually increase poll interval to reduce API calls
                        poll_interval = min(poll_interval + 2, 15)
                        continue

                    else:
                        # Unknown status, log it and continue polling
                        logger.warning(f"Unknown status '{status}' for generation {generation_id}")
                        await asyncio.sleep(poll_interval)
                        continue

            except Exception as e:
                logger.error(f"Error during polling: {e}")
                await asyncio.sleep(poll_interval)
                continue

        # Timeout reached
        logger.error(f"Generation {generation_id} timed out after {max_wait_time} seconds")
        return {"success": False, "error": f"Generation timed out after {max_wait_time} seconds"}

    async def get_generation_status(self, generation_id: str) -> Dict[str, Any]:
        """
        Get the current status of a generation.

        Args:
            generation_id: The ID of the generation to check

        Returns:
            Dict with generation status and data
        """
        if not self.api_key:
            return {"success": False, "error": "Luma Labs API key not configured"}

        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(
                    f"{self.base_url}/{generation_id}",
                    headers=headers
                )

                if response.status_code != 200:
                    error_text = response.text
                    logger.error(f"Error getting generation status: {response.status_code} - {error_text}")
                    return {"success": False, "error": f"API error: {error_text}"}

                result = response.json()
                return {
                    "success": True,
                    "generation_id": generation_id,
                    "status": result.get("state", "unknown"),
                    "data": result
                }

        except Exception as e:
            logger.error(f"Error getting generation status: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}

    def validate_resolution(self, resolution: str) -> bool:
        """Validate if the resolution is supported."""
        valid_resolutions = ["720p", "1080p", "4K"]
        return resolution in valid_resolutions

    def validate_aspect_ratio(self, aspect_ratio: str) -> bool:
        """Validate if the aspect ratio is supported."""
        valid_ratios = ["16:9", "1:1", "9:16"]
        return aspect_ratio in valid_ratios


# Global service instance
luma_service = LumaService()
