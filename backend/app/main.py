from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse
from fastapi.exceptions import RequestValidationError
import os
import tempfile

from app.api.endpoints import health, images, external_services, content, crew, videos, sketches, design_analysis, focus_group, headline_analyzer, premium_features, erase, inpaint, outpaint, search_replace, search_recolor, replace_background, upscale, agenticseek, agenticseek_cloud, generate_3d, web_analysis, posts, infographics, posters, memes, ads, seo_analysis, openai_images, ad_creator_agent, mockups, image_generator, influencers, luma_labs
from app.api.v1 import logo_text, openai_images, image_text

from app.api.endpoints import buyer_persona
# Temporarily disabled: agents
# from app.api.endpoints import stability_background_removal
# from app.api.v1.endpoints import websockets  # Temporarily disabled
from app.core.config import settings
from app.core.logging import setup_logging
from app.exceptions.handlers import (
    http_exception_handler,
    validation_exception_handler,
    generic_exception_handler
)

# Setup logging
logger = setup_logging()

app = FastAPI(
    title="Emma Studio API",
    description="Backend API for Emma Studio content generation platform",
    version="1.0.0",
    docs_url="/api/v1/docs",
    openapi_url="/api/v1/openapi.json"
)

# CORS configuration with environment-specific settings
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=settings.ALLOWED_METHODS,
    allow_headers=settings.ALLOWED_HEADERS,
)

# Register routers with consistent /api/v1 prefix
app.include_router(health.router, prefix="/api/v1", tags=["health"])
# app.include_router(agents.router, prefix="/api/v1", tags=["agents"])  # Temporarily disabled
app.include_router(content.router, prefix="/api/v1/content", tags=["content"])
app.include_router(crew.router, prefix="/api/v1/crew", tags=["crew"])
app.include_router(images.router, prefix="/api/v1/images", tags=["images"])
app.include_router(videos.router, prefix="/api/v1/videos", tags=["videos"])
app.include_router(sketches.router, prefix="/api/v1/sketches", tags=["sketches"])
app.include_router(erase.router, prefix="/api/v1/ai-editor", tags=["ai-image-editor"])
app.include_router(inpaint.router, prefix="/api/v1/ai-editor", tags=["ai-image-editor"])
app.include_router(outpaint.router, prefix="/api/v1/ai-editor", tags=["ai-image-editor"])
app.include_router(search_replace.router, prefix="/api/v1/ai-editor", tags=["ai-image-editor"])
app.include_router(search_recolor.router, prefix="/api/v1/ai-editor", tags=["ai-image-editor"])
app.include_router(external_services.router, prefix="/api/v1/external", tags=["external-services"])

# app.include_router(websockets.router, prefix="/api/v1", tags=["websockets"])  # Temporarily disabled

# Legacy endpoints for compatibility with frontend
app.include_router(design_analysis.router, prefix="/api", tags=["design-analysis"])
app.include_router(focus_group.router, prefix="/api", tags=["focus-group"])
app.include_router(headline_analyzer.router, prefix="/api", tags=["headline-analyzer"])
app.include_router(buyer_persona.router, prefix="/api", tags=["buyer-persona"])

# Premium features endpoints
app.include_router(premium_features.router, prefix="/api/v1/premium", tags=["premium-features"])

# AgenticSeek endpoints (original with Docker dependencies)
app.include_router(agenticseek.router, prefix="/api/v1", tags=["agenticseek"])

# AgenticSeek Cloud endpoints (NO Docker, cloud-native)
app.include_router(agenticseek_cloud.router, prefix="/api/v1", tags=["agenticseek-cloud"])

# Legacy endpoint for frontend compatibility
app.include_router(images.bg_removal_router, prefix="/api/stability-remove-bg", tags=["stability-background-removal"])

# Replace background endpoint for frontend compatibility
app.include_router(replace_background.router, prefix="/api/stability-replace-bg", tags=["stability-replace-background"])

# Upscale (image quality enhancement) endpoint
app.include_router(upscale.router, prefix="/api/v1/ai-editor", tags=["ai-editor-upscale"])

# 3D generation endpoint
app.include_router(generate_3d.router, prefix="/api/v1/images", tags=["3d-generation"])

# Web analysis endpoint (Jina AI + Gemini)
app.include_router(web_analysis.router, prefix="/api/v1/web", tags=["web-analysis"])

# SEO analysis endpoint (Jina AI + Gemini + PageSpeed Insights)
app.include_router(seo_analysis.router, prefix="/api/seo", tags=["seo-analysis"])

# Posts generation endpoint (GPT-4 + Stability AI + Gemini)
app.include_router(posts.router, prefix="/api/v1/posts", tags=["posts"])

# Infographics endpoint (OpenAI gpt-image-1)
app.include_router(infographics.router, prefix="/api/v1/infographics", tags=["infographics"])

# Posters endpoint (OpenAI gpt-image-1)
app.include_router(posters.router, prefix="/api/posters", tags=["posters"])

# Memes endpoint (OpenAI gpt-image-1)
app.include_router(memes.router, prefix="/api/memes", tags=["memes"])

# Ads endpoint (Ideogram 3.0 Quality)
app.include_router(ads.router, prefix="/api/ads", tags=["ads"])

# Mockups endpoint (Ideogram 3.0 Quality)
app.include_router(mockups.router, prefix="/api/mockups", tags=["mockups"])

# Ad Creator Agent endpoint (Specialized Emma for ad creation)
app.include_router(ad_creator_agent.router, tags=["ad-creator-agent"])

# Logo Text endpoint (OpenAI gpt-image-1)
app.include_router(logo_text.router, prefix="/api/v1/logo-text", tags=["logo-text"])

# OpenAI Images endpoint (OpenAI gpt-image-1)
app.include_router(openai_images.router, prefix="/api/v1/openai-images", tags=["openai-images"])

# Image Text endpoint (OpenAI gpt-image-1)
app.include_router(image_text.router, prefix="/api/v1/image-text", tags=["image-text"])

# Image Generator endpoint (Ideogram AI v3)
app.include_router(image_generator.router, prefix="/api/image-generator", tags=["image-generator"])

# Influencers endpoint (Ideogram AI v3)
app.include_router(influencers.router, prefix="/api/influencers", tags=["influencers"])

# Luma Labs endpoint (Dream Machine API)
app.include_router(luma_labs.router, prefix="/api/luma-labs", tags=["luma-labs"])

# Main images endpoint (includes style-reference)
app.include_router(images.router, prefix="/api/v1/images", tags=["images"])

# Endpoint to serve temporary files (for image generation services)
@app.get("/temp/{filename}")
async def serve_temp_file(filename: str):
    """Serve temporary files generated by image services."""
    try:
        # Get the system temp directory
        temp_dir = tempfile.gettempdir()
        file_path = os.path.join(temp_dir, filename)

        # Security check: ensure the file exists and is within temp directory
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="File not found")

        # Additional security: ensure the path is within temp directory
        if not os.path.commonpath([temp_dir, file_path]) == temp_dir:
            raise HTTPException(status_code=403, detail="Access denied")

        # Determine media type based on file extension
        if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
            media_type = "image/png" if filename.lower().endswith('.png') else "image/jpeg"
        else:
            media_type = "application/octet-stream"

        return FileResponse(
            file_path,
            media_type=media_type,
            filename=filename,
            headers={"Cache-Control": "no-cache, no-store, must-revalidate"}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error serving temp file {filename}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

# Exception handlers
app.add_exception_handler(RequestValidationError, validation_exception_handler)
app.add_exception_handler(Exception, generic_exception_handler)

@app.middleware("http")
async def log_requests(request: Request, call_next):
    try:
        response = await call_next(request)
        logger.info(f"Request: {request.method} {request.url.path} Status: {response.status_code}")
        return response
    except Exception as e:
        logger.error(f"Request failed: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"detail": "Internal server error"}
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.main:app", host=settings.HOST, port=settings.PORT, reload=True)