"""
API endpoints for Luma Labs video generation using Dream Machine API.
"""

import logging
import base64
import tempfile
import os
from fastapi import APIRouter, Depends, HTTPException, Form, UploadFile, File, Query
from fastapi.responses import StreamingResponse, Response
from typing import Optional, List
import json
import httpx

from app.core.auth import verify_api_key
from app.services.luma_service import luma_service
from app.schemas.luma import (
    LumaTextToVideoRequest,
    LumaImageToVideoRequest,
    FrontendLumaResponse,
    LumaGenerationStatusResponse,
    FrontendStreamResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post(
    "/generate-text-to-video",
    response_model=FrontendLumaResponse,
    dependencies=[Depends(verify_api_key)],
)
async def generate_text_to_video(
    prompt: str = Form(..., description="Text description of the video"),
    resolution: str = Form(default="720p", description="Video resolution: 720p, 1080p, 4K"),
    duration: int = Form(default=5, description="Video duration in seconds (1-10)"),
    aspect_ratio: str = Form(default="16:9", description="Aspect ratio: 16:9, 1:1, 9:16"),
    loop: bool = Form(default=False, description="Whether video should loop"),
    concepts: Optional[str] = Form(default=None, description="Special effects (comma-separated)")
) -> FrontendLumaResponse:
    """Generate a video from text using Luma Labs Dream Machine."""

    try:
        logger.info(f"🎬 Generating text-to-video with Luma Labs: {prompt[:100]}...")

        # Validate inputs
        if not luma_service.validate_resolution(resolution):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid resolution. Must be one of: 720p, 1080p, 4K"
            )

        if not luma_service.validate_aspect_ratio(aspect_ratio):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid aspect ratio. Must be one of: 16:9, 1:1, 9:16"
            )

        if duration not in [5, 9]:
            raise HTTPException(
                status_code=400,
                detail="Duration must be 5 or 9 seconds (Luma Labs API limitation)"
            )

        # Parse concepts if provided
        concepts_list = None
        if concepts:
            concepts_list = [c.strip() for c in concepts.split(",") if c.strip()]

        # Call the service
        service_response = await luma_service.generate_text_to_video(
            prompt=prompt,
            resolution=resolution,
            duration=duration,
            aspect_ratio=aspect_ratio,
            loop=loop,
            concepts=concepts_list
        )

        # Convert to frontend response
        return FrontendLumaResponse.from_service_response(service_response)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in generate_text_to_video endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during video generation: {e}"
        )


@router.post(
    "/generate-image-to-video",
    response_model=FrontendLumaResponse,
    dependencies=[Depends(verify_api_key)],
)
async def generate_image_to_video(
    prompt: str = Form(..., description="Text description of the video"),
    resolution: str = Form(default="720p", description="Video resolution: 720p, 1080p, 4K"),
    duration: int = Form(default=5, description="Video duration in seconds (1-10)"),
    aspect_ratio: str = Form(default="16:9", description="Aspect ratio: 16:9, 1:1, 9:16"),
    loop: bool = Form(default=False, description="Whether video should loop"),
    keyframe_type: str = Form(..., description="Keyframe type: frame0, frame1, or both"),
    frame0: Optional[UploadFile] = File(default=None, description="Initial frame image"),
    frame1: Optional[UploadFile] = File(default=None, description="Final frame image")
) -> FrontendLumaResponse:
    """Generate a video from image keyframes using Luma Labs Dream Machine."""

    try:
        logger.info(f"🎬 Generating image-to-video with Luma Labs: {prompt[:100]}...")

        # Validate inputs
        if not luma_service.validate_resolution(resolution):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid resolution. Must be one of: 720p, 1080p, 4K"
            )

        if not luma_service.validate_aspect_ratio(aspect_ratio):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid aspect ratio. Must be one of: 16:9, 1:1, 9:16"
            )

        if duration not in [5, 9]:
            raise HTTPException(
                status_code=400,
                detail="Duration must be 5 or 9 seconds (Luma Labs API limitation)"
            )

        if keyframe_type not in ["frame0", "frame1", "both"]:
            raise HTTPException(
                status_code=400,
                detail="keyframe_type must be 'frame0', 'frame1', or 'both'"
            )

        # Validate required files based on keyframe type
        if keyframe_type == "frame0" and not frame0:
            raise HTTPException(status_code=400, detail="frame0 image is required")
        if keyframe_type == "frame1" and not frame1:
            raise HTTPException(status_code=400, detail="frame1 image is required")
        if keyframe_type == "both" and (not frame0 or not frame1):
            raise HTTPException(status_code=400, detail="Both frame0 and frame1 images are required")

        # Process uploaded images
        keyframes = {}
        
        if frame0:
            # Validate file type
            if not frame0.content_type or not frame0.content_type.startswith("image/"):
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid frame0 file type: {frame0.content_type}"
                )
            
            # Read and encode image
            frame0_content = await frame0.read()
            frame0_base64 = base64.b64encode(frame0_content).decode("utf-8")
            keyframes["frame0"] = f"data:{frame0.content_type};base64,{frame0_base64}"

        if frame1:
            # Validate file type
            if not frame1.content_type or not frame1.content_type.startswith("image/"):
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid frame1 file type: {frame1.content_type}"
                )
            
            # Read and encode image
            frame1_content = await frame1.read()
            frame1_base64 = base64.b64encode(frame1_content).decode("utf-8")
            keyframes["frame1"] = f"data:{frame1.content_type};base64,{frame1_base64}"

        # Call the service
        service_response = await luma_service.generate_image_to_video(
            prompt=prompt,
            keyframes=keyframes,
            resolution=resolution,
            duration=duration,
            aspect_ratio=aspect_ratio,
            loop=loop
        )

        # Convert to frontend response
        return FrontendLumaResponse.from_service_response(service_response)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in generate_image_to_video endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during video generation: {e}"
        )


@router.get(
    "/status/{generation_id}",
    response_model=LumaGenerationStatusResponse,
    dependencies=[Depends(verify_api_key)],
)
async def get_generation_status(generation_id: str) -> LumaGenerationStatusResponse:
    """Get the status of a video generation."""
    
    try:
        logger.info(f"📊 Getting status for generation: {generation_id}")

        service_response = await luma_service.get_generation_status(generation_id)

        return LumaGenerationStatusResponse(
            success=service_response.get("success", False),
            generation_id=generation_id,
            status=service_response.get("status", "unknown"),
            data=service_response.get("data"),
            error=service_response.get("error")
        )
        
    except Exception as e:
        logger.error(f"Error in get_generation_status endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error getting generation status: {e}"
        )


@router.get("/download-video")
async def download_video(
    url: str = Query(..., description="The video URL to download")
):
    """
    Proxy endpoint to download videos from Luma Labs
    and serve them directly to bypass CORS restrictions.
    """
    try:
        logger.info(f"📥 Proxying video download from: {url[:100]}...")

        # Validate that the URL is from a trusted source (Luma Labs)
        if not url.startswith("https://storage.googleapis.com/luma-dream-machine"):
            raise HTTPException(
                status_code=400,
                detail="Only Luma Labs video URLs are allowed"
            )

        async with httpx.AsyncClient(timeout=300.0) as client:
            # Stream the video content
            async with client.stream("GET", url) as response:
                if response.status_code != 200:
                    logger.error(f"Failed to download video: {response.status_code}")
                    raise HTTPException(
                        status_code=response.status_code,
                        detail=f"Failed to download video: {response.status_code}"
                    )

                # Get content type from the response
                content_type = response.headers.get("content-type", "video/mp4")

                # Determine file extension based on content type
                if "mp4" in content_type:
                    file_extension = "mp4"
                elif "webm" in content_type:
                    file_extension = "webm"
                elif "mov" in content_type:
                    file_extension = "mov"
                else:
                    file_extension = "mp4"  # Default fallback

                # Create filename with timestamp
                import time
                filename = f"video-{int(time.time())}.{file_extension}"

                # Read the entire content
                content = b""
                async for chunk in response.aiter_bytes():
                    content += chunk

                # Return the video as a downloadable response
                return Response(
                    content=content,
                    media_type=content_type,
                    headers={
                        "Content-Disposition": f"attachment; filename={filename}",
                        "Content-Length": str(len(content)),
                        "Cache-Control": "no-cache"
                    }
                )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in download_video endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during video download: {e}"
        )
